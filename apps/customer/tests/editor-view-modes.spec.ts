  import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Editor View Modes', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
    
    // Add console logging for debugging
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });
  })

  test('should display editor in edit mode by default', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })
    
    // Editor should be editable by default
    await expect(editor).toHaveAttribute('contenteditable', 'true')
    
    // Editor should accept text input
    await editor.click()
    await page.keyboard.type('Test editable content')
    
    // Content should appear
    await expect(editor.locator('text=Test editable content')).toBeVisible()
    
    // Main toolbar should be visible
    await expect(page.locator('[data-testid="editor-toolbar"]')).toBeVisible()
    
    // Collaboration toolbar should be visible
    await expect(page.locator('[data-testid="collaboration-toolbar"]')).toBeVisible()
    
    // Editor should be part of the main editor container
    await expect(page.locator('[data-testid="eko-document-editor"]')).toBeVisible()
  })

  test('should toggle print mode using print button', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })
    
    // Add some content first
    await editor.click()
    await page.keyboard.type('# Print Mode Test\n\nThis content will be styled for printing.')
    
    // Wait for auto-save
    await page.waitForTimeout(2000)
    
    // Find and click the print mode toggle button
    const printButton = page.locator('[data-testid="print-toggle-button"]')
    await expect(printButton).toBeVisible()
    await expect(printButton).toContainText('Print')
    await printButton.click()
    
    // Button text should change to "Normal"
    await expect(printButton).toContainText('Normal')
    
    // Editor should have print mode class applied
    await expect(editor).toHaveClass(/eko-print-mode/)
    
    // Click Normal to exit print mode
    await printButton.click()
    
    // Button should change back to "Print"
    await expect(printButton).toContainText('Print')
    
    // Print mode class should be removed
    await expect(editor).not.toHaveClass(/eko-print-mode/)
  })

  test('should work in view mode when configured', async ({ page }) => {
    // Navigate directly to a view-only page (if such routes exist)
    // For now, we'll test the print page which uses view mode
    const documentId = await testUtils.createDocumentFromTemplate()
    
    // Wait for document to be saved before navigating to print view
    await page.waitForResponse(response => 
      response.url().includes(`/documents/${documentId}/save`) && response.status() === 200
    );
    
    // Navigate to print view which uses viewMode=true
    await page.goto(`/documents/${documentId}/print`)
    
    // Wait for print page to load
    await page.waitForLoadState('networkidle')
    
    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })
    
    // Editor should be read-only in view mode
    await expect(editor).toHaveAttribute('contenteditable', 'false')
    
    // Toolbars should be hidden in view mode
    await expect(page.locator('[data-testid="editor-toolbar"]')).not.toBeVisible()
    await expect(page.locator('[data-testid="collaboration-toolbar"]')).not.toBeVisible()
    
    // Content should still be visible and readable
    await expect(editor).toBeVisible()
    
    // Should have print mode class applied
    await expect(editor).toHaveClass(/eko-print-mode/)
  })

  test('should display save button in collaboration toolbar', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })
    
    // Add content to trigger save functionality
    await testUtils.typeInEditor('Test save status')
    
    // Check that save button exists in collaboration toolbar
    await expect(page.locator('[data-testid="collaboration-toolbar"] button[title="Save Document"]')).toBeVisible()
  })

  test('should handle keyboard shortcuts for formatting', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })
    
    // Test bold shortcut
    await testUtils.typeInEditor('Test text')
    await page.keyboard.press('ControlOrMeta+a')
    await page.keyboard.press('ControlOrMeta+b')
    
    // Text should be bold
    await expect(editor.locator('strong')).toBeVisible({ timeout: 5000 })
    
    // Test italic shortcut
    await page.keyboard.press('ControlOrMeta+a')
    await page.keyboard.press('ControlOrMeta+i')
    
    // Text should be italic (might be both bold and italic)
    await expect(editor.locator('em, strong em, em strong')).toBeVisible({ timeout: 5000 })
  })

  test('should render toolbar buttons correctly', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })
    
    // Check main formatting buttons exist
    await expect(page.locator('[data-testid="editor-toolbar"] button[title="Bold"]')).toBeVisible()
    await expect(page.locator('[data-testid="editor-toolbar"] button[title="Italic"]')).toBeVisible()
    await expect(page.locator('[data-testid="editor-toolbar"] button[title="Underline"]')).toBeVisible()
    
    // Check table and image buttons exist
    await expect(page.locator('[data-testid="editor-toolbar"] button[title="Insert Table"]')).toBeVisible()
    await expect(page.locator('[data-testid="editor-toolbar"] button[title="Insert Image"]')).toBeVisible()
    
    // Check undo/redo buttons exist
    await expect(page.locator('[data-testid="editor-toolbar"] button[title="Undo"]')).toBeVisible()
    await expect(page.locator('[data-testid="editor-toolbar"] button[title="Redo"]')).toBeVisible()
    
    // Check save button exists
    await expect(page.locator('[data-testid="collaboration-toolbar"] button[title="Save Document"]')).toBeVisible()
  })

  test('should handle table insertion', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })
    
    // Click at the beginning of the editor
    await editor.click()
    
    // Find and click table button in editor toolbar
    const tableButton = page.locator('[data-testid="editor-toolbar"] button[title="Insert Table"]')
    await expect(tableButton).toBeVisible()
    await tableButton.click()
    
    // Table should be created
    await expect(editor.locator('table')).toBeVisible({ timeout: 5000 })
    
    // Should have either header cells or data cells
    const headerCells = editor.locator('th')
    const dataCells = editor.locator('td')
    
    // At least one type of cell should exist
    const headerCount = await headerCells.count()
    const dataCount = await dataCells.count()
    expect(headerCount + dataCount).toBeGreaterThan(0)
  })

  test('should handle mobile viewport responsively', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })
    
    // Toolbar should still be visible on mobile
    await expect(page.locator('[data-testid="editor-toolbar"]')).toBeVisible()
    
    // Editor should be touch-friendly and functional
    await testUtils.typeInEditor('Mobile editing test')
    await expect(editor.locator('text=Mobile editing test')).toBeVisible()
    
    // Toolbar buttons should still be clickable
    await expect(page.locator('[data-testid="editor-toolbar"] button[title="Bold"]')).toBeVisible()
  })

  test('should preserve content when navigating between pages', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })
    
    // Add content
    await testUtils.typeInEditor('# Content Preservation Test\n\nThis content should persist.')
    
    // Wait for auto-save
    await page.waitForTimeout(3000)
    await page.waitForLoadState('networkidle')
    
    // Navigate away and back
    await page.goto('/customer/dashboard')
    await page.waitForLoadState('networkidle')
    
    // Navigate back to the document
    await page.goto(`/customer/documents/${documentId}`)
    await page.waitForLoadState('networkidle')
    
    // Wait for editor to load
    const newEditor = await testUtils.waitForEditor()
    await expect(newEditor).toBeVisible({ timeout: 10000 })
    
    // Content should be preserved
    await expect(newEditor.locator('text=Content Preservation Test')).toBeVisible({ timeout: 10000 })
    await expect(newEditor.locator('text=This content should persist.')).toBeVisible({ timeout: 10000 })
  })

  test('should handle export functionality', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })
    
    // Add some content to export
    await testUtils.typeInEditor('# Export Test\n\nThis document will be exported.')
    
    // Check that collaboration toolbar exists (export may be conditional)
    await expect(page.locator('[data-testid="collaboration-toolbar"]')).toBeVisible()
    
    // If export button exists, test it
    const exportButton = page.locator('[data-testid="collaboration-toolbar"] button[title*="Export"]')
    if (await exportButton.isVisible()) {
      await exportButton.click()
      await expect(page.locator('text=Export as PDF')).toBeVisible()
    }
  })
})
